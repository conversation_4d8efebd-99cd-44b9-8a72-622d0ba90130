import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    strictPort: true, // 如果端口被占用，会报错而不是尝试其他端口
    proxy: {
      "/api/dashscope": {
        target: "https://dashscope.aliyuncs.com",
        changeOrigin: true,
        rewrite: (path) => {
          // 修复1: 确保没有双斜杠问题
          const cleanPath = path.replace(/^\/api\/dashscope/, "");
          return cleanPath.replace(/\/+/g, "/");
        },
        // 修复2: 在生产环境使用安全连接
        secure: true,
        // 修复3: 使用更高效的日志方式
        configure: (proxy, _options) => {
          // 只记录错误日志，减少不必要的日志输出
          proxy.on("error", (err, _req, _res) => {
            console.error("代理错误:", err);
          });
          
          // 修复4: 移除不必要的日志记录，只保留关键错误信息
          // 如果需要调试，可以临时添加更多日志
        },
      },
    },
  },
});
