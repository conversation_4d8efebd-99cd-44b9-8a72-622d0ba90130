{"name": "antd-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd": "^5.25.3", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash": "^4.17.17", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}