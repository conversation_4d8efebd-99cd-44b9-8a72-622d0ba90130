# AI 便签应用性能优化与最佳实践

## 🚀 性能优化策略

### 1. React 组件优化

#### 1.1 使用 React.memo 防止不必要的重渲染
```typescript
// 优化便签组件
const StickyNote = React.memo<StickyNoteProps>(
  ({ note, onUpdate, onDelete, canvasScale, canvasOffset }) => {
    // 组件实现
    return (
      <div className="sticky-note">
        {/* 便签内容 */}
      </div>
    );
  },
  // 自定义比较函数
  (prevProps, nextProps) => {
    return (
      prevProps.note.id === nextProps.note.id &&
      prevProps.note.content === nextProps.note.content &&
      prevProps.note.x === nextProps.note.x &&
      prevProps.note.y === nextProps.note.y &&
      prevProps.note.width === nextProps.note.width &&
      prevProps.note.height === nextProps.note.height &&
      prevProps.canvasScale === nextProps.canvasScale &&
      prevProps.canvasOffset.x === nextProps.canvasOffset.x &&
      prevProps.canvasOffset.y === nextProps.canvasOffset.y
    );
  }
);
```

#### 1.2 使用 useMemo 缓存计算结果
```typescript
const InfiniteCanvas = () => {
  const [stickyNotes, setStickyNotes] = useState<StickyNote[]>([]);
  const [canvasState, setCanvasState] = useState<CanvasState>(defaultState);

  // 缓存可见便签的计算
  const visibleNotes = useMemo(() => {
    const viewport = {
      left: -canvasState.offsetX / canvasState.scale,
      top: -canvasState.offsetY / canvasState.scale,
      right: (-canvasState.offsetX + window.innerWidth) / canvasState.scale,
      bottom: (-canvasState.offsetY + window.innerHeight) / canvasState.scale,
    };

    return stickyNotes.filter(note => {
      return (
        note.x < viewport.right &&
        note.x + note.width > viewport.left &&
        note.y < viewport.bottom &&
        note.y + note.height > viewport.top
      );
    });
  }, [stickyNotes, canvasState.offsetX, canvasState.offsetY, canvasState.scale]);

  // 缓存排序后的便签
  const sortedNotes = useMemo(() => {
    return [...visibleNotes].sort((a, b) => a.zIndex - b.zIndex);
  }, [visibleNotes]);

  return (
    <div className="infinite-canvas">
      {sortedNotes.map(note => (
        <StickyNote key={note.id} note={note} {...otherProps} />
      ))}
    </div>
  );
};
```

#### 1.3 使用 useCallback 缓存事件处理函数
```typescript
const InfiniteCanvas = () => {
  const [stickyNotes, setStickyNotes] = useState<StickyNote[]>([]);

  // 缓存更新函数
  const handleNoteUpdate = useCallback((id: string, updates: Partial<StickyNote>) => {
    setStickyNotes(prev => 
      prev.map(note => 
        note.id === id ? { ...note, ...updates, updatedAt: new Date() } : note
      )
    );
  }, []);

  // 缓存删除函数
  const handleNoteDelete = useCallback((id: string) => {
    setStickyNotes(prev => prev.filter(note => note.id !== id));
  }, []);

  // 缓存置顶函数
  const handleBringToFront = useCallback((id: string) => {
    setStickyNotes(prev => {
      const maxZ = Math.max(...prev.map(note => note.zIndex));
      return prev.map(note => 
        note.id === id ? { ...note, zIndex: maxZ + 1 } : note
      );
    });
  }, []);

  return (
    <div className="infinite-canvas">
      {stickyNotes.map(note => (
        <StickyNote
          key={note.id}
          note={note}
          onUpdate={handleNoteUpdate}
          onDelete={handleNoteDelete}
          onBringToFront={handleBringToFront}
        />
      ))}
    </div>
  );
};
```

### 2. 虚拟化渲染优化

#### 2.1 实现视口裁剪
```typescript
// 视口裁剪 Hook
const useViewportCulling = (
  items: StickyNote[],
  canvasState: CanvasState,
  containerRef: RefObject<HTMLElement>
) => {
  return useMemo(() => {
    if (!containerRef.current) return items;

    const container = containerRef.current.getBoundingClientRect();
    const viewport = {
      left: -canvasState.offsetX / canvasState.scale,
      top: -canvasState.offsetY / canvasState.scale,
      right: (-canvasState.offsetX + container.width) / canvasState.scale,
      bottom: (-canvasState.offsetY + container.height) / canvasState.scale,
    };

    // 添加边距以支持平滑滚动
    const margin = 200;
    viewport.left -= margin;
    viewport.top -= margin;
    viewport.right += margin;
    viewport.bottom += margin;

    return items.filter(note => {
      return (
        note.x < viewport.right &&
        note.x + note.width > viewport.left &&
        note.y < viewport.bottom &&
        note.y + note.height > viewport.top
      );
    });
  }, [items, canvasState, containerRef]);
};
```

#### 2.2 分层渲染优化
```typescript
// 分层渲染组件
const LayeredCanvas = ({ notes }: { notes: StickyNote[] }) => {
  // 按 Z-index 分层
  const layers = useMemo(() => {
    const layerMap = new Map<number, StickyNote[]>();
    
    notes.forEach(note => {
      const layer = Math.floor(note.zIndex / 100) * 100;
      if (!layerMap.has(layer)) {
        layerMap.set(layer, []);
      }
      layerMap.get(layer)!.push(note);
    });

    return Array.from(layerMap.entries())
      .sort(([a], [b]) => a - b)
      .map(([zIndex, layerNotes]) => ({ zIndex, notes: layerNotes }));
  }, [notes]);

  return (
    <div className="layered-canvas">
      {layers.map(({ zIndex, notes: layerNotes }) => (
        <div key={zIndex} className="canvas-layer" style={{ zIndex }}>
          {layerNotes.map(note => (
            <StickyNote key={note.id} note={note} />
          ))}
        </div>
      ))}
    </div>
  );
};
```

### 3. AI 服务优化

#### 3.1 请求缓存机制
```typescript
class OptimizedAIService extends AIService {
  private cache = new Map<string, { result: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  async generateStickyNotes(prompt: string): Promise<GenerateResult> {
    // 检查缓存
    const cacheKey = this.getCacheKey(prompt);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log('🎯 使用缓存结果');
      return cached.result;
    }

    // 调用原始方法
    const result = await super.generateStickyNotes(prompt);
    
    // 缓存成功结果
    if (result.success) {
      this.cache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });
    }

    return result;
  }

  private getCacheKey(prompt: string): string {
    // 标准化提示词作为缓存键
    return prompt.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  // 清理过期缓存
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.CACHE_TTL) {
        this.cache.delete(key);
      }
    }
  }
}
```

#### 3.2 请求防抖和节流
```typescript
// 防抖 Hook
const useDebouncedAIGeneration = (delay: number = 1000) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedGenerate = useCallback(
    (prompt: string, generateFn: (prompt: string) => Promise<void>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      setIsGenerating(true);
      
      timeoutRef.current = setTimeout(async () => {
        try {
          await generateFn(prompt);
        } finally {
          setIsGenerating(false);
        }
      }, delay);
    },
    [delay]
  );

  const cancelGeneration = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      setIsGenerating(false);
    }
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { debouncedGenerate, cancelGeneration, isGenerating };
};
```

### 4. 数据存储优化

#### 4.1 批量操作优化
```typescript
class OptimizedIndexedDBService {
  // 批量保存便签
  static async batchSaveNotes(notes: StickyNote[]): Promise<void> {
    const db = await this.openDB();
    const transaction = db.transaction([this.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(this.STORE_NAME);

    // 使用 Promise.all 并行处理
    const promises = notes.map(note => store.put(note));
    await Promise.all(promises);
    await transaction.complete;
  }

  // 增量同步
  static async syncChanges(changes: NoteChange[]): Promise<void> {
    const db = await this.openDB();
    const transaction = db.transaction([this.STORE_NAME], 'readwrite');
    const store = transaction.objectStore(this.STORE_NAME);

    for (const change of changes) {
      switch (change.type) {
        case 'create':
        case 'update':
          await store.put(change.note);
          break;
        case 'delete':
          await store.delete(change.noteId);
          break;
      }
    }

    await transaction.complete;
  }

  // 分页加载
  static async loadNotesPage(
    page: number, 
    pageSize: number = 50
  ): Promise<StickyNote[]> {
    const db = await this.openDB();
    const transaction = db.transaction([this.STORE_NAME], 'readonly');
    const store = transaction.objectStore(this.STORE_NAME);
    
    const offset = page * pageSize;
    const notes: StickyNote[] = [];
    
    let cursor = await store.openCursor();
    let count = 0;
    
    while (cursor && notes.length < pageSize) {
      if (count >= offset) {
        notes.push(cursor.value);
      }
      count++;
      cursor = await cursor.continue();
    }
    
    return notes;
  }
}
```

#### 4.2 数据压缩存储
```typescript
// 数据压缩工具
class DataCompression {
  // 压缩便签数据
  static compressNote(note: StickyNote): CompressedNote {
    return {
      id: note.id,
      x: Math.round(note.x),
      y: Math.round(note.y),
      w: note.width,
      h: note.height,
      c: note.content,
      t: note.title,
      col: note.color[0], // 只存储颜色首字母
      z: note.zIndex,
      ca: note.createdAt.getTime(),
      ua: note.updatedAt.getTime()
    };
  }

  // 解压便签数据
  static decompressNote(compressed: CompressedNote): StickyNote {
    const colorMap: Record<string, StickyNote['color']> = {
      'y': 'yellow',
      'b': 'blue', 
      'g': 'green',
      'p': 'pink',
      'u': 'purple'
    };

    return {
      id: compressed.id,
      x: compressed.x,
      y: compressed.y,
      width: compressed.w,
      height: compressed.h,
      content: compressed.c,
      title: compressed.t,
      color: colorMap[compressed.col] || 'yellow',
      isEditing: false,
      isTitleEditing: false,
      isNew: false,
      zIndex: compressed.z,
      createdAt: new Date(compressed.ca),
      updatedAt: new Date(compressed.ua)
    };
  }
}
```

## 🎯 最佳实践

### 1. 代码组织

#### 1.1 模块化架构
```
src/
├── components/          # UI 组件
│   ├── common/         # 通用组件
│   ├── canvas/         # 画布相关组件
│   └── notes/          # 便签相关组件
├── hooks/              # 自定义 Hooks
├── services/           # 业务服务
├── utils/              # 工具函数
├── types/              # 类型定义
└── constants/          # 常量定义
```

#### 1.2 类型安全
```typescript
// 严格的类型定义
interface StrictStickyNote {
  readonly id: string;
  readonly x: number;
  readonly y: number;
  readonly width: number;
  readonly height: number;
  readonly content: string;
  readonly title: string;
  readonly color: 'yellow' | 'blue' | 'green' | 'pink' | 'purple';
  readonly zIndex: number;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  
  // 可变状态
  isEditing: boolean;
  isTitleEditing: boolean;
  isNew: boolean;
}

// 类型守卫
function isStickyNote(obj: any): obj is StickyNote {
  return (
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.x === 'number' &&
    typeof obj.y === 'number' &&
    typeof obj.width === 'number' &&
    typeof obj.height === 'number' &&
    typeof obj.content === 'string' &&
    typeof obj.title === 'string' &&
    ['yellow', 'blue', 'green', 'pink', 'purple'].includes(obj.color) &&
    typeof obj.zIndex === 'number' &&
    obj.createdAt instanceof Date &&
    obj.updatedAt instanceof Date
  );
}
```

### 2. 错误处理

#### 2.1 全局错误边界
```typescript
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('应用错误:', error, errorInfo);
    
    // 发送错误报告
    this.reportError(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // 实现错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    // 发送到错误监控服务
    console.log('错误报告:', errorReport);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>应用出现错误</h2>
          <p>请刷新页面重试，如果问题持续存在，请联系技术支持。</p>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

#### 2.2 异步错误处理
```typescript
// 统一的异步错误处理
const useAsyncError = () => {
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  const executeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await asyncFn();
      onSuccess?.(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      onError?.(error);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => setError(null), []);

  return { error, loading, executeAsync, clearError };
};
```

### 3. 性能监控

#### 3.1 性能指标收集
```typescript
class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();

  // 测量函数执行时间
  static measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    this.recordMetric(name, duration);
    return result;
  }

  // 测量异步函数执行时间
  static async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    this.recordMetric(name, duration);
    return result;
  }

  // 记录指标
  private static recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 保持最近 100 个数据点
    if (values.length > 100) {
      values.shift();
    }
  }

  // 获取性能报告
  static getReport(): PerformanceReport {
    const report: PerformanceReport = {};
    
    for (const [name, values] of this.metrics.entries()) {
      const avg = values.reduce((a, b) => a + b, 0) / values.length;
      const min = Math.min(...values);
      const max = Math.max(...values);
      
      report[name] = { avg, min, max, count: values.length };
    }
    
    return report;
  }
}
```

### 4. 内存管理

#### 4.1 清理副作用
```typescript
// 自动清理 Hook
const useCleanup = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanup = useCallback((fn: () => void) => {
    cleanupFunctions.current.push(fn);
  }, []);

  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(fn => fn());
      cleanupFunctions.current = [];
    };
  }, []);

  return addCleanup;
};

// 使用示例
const MyComponent = () => {
  const addCleanup = useCleanup();

  useEffect(() => {
    const timer = setInterval(() => {
      // 定时任务
    }, 1000);

    const subscription = eventEmitter.subscribe('event', handler);

    // 注册清理函数
    addCleanup(() => clearInterval(timer));
    addCleanup(() => subscription.unsubscribe());
  }, [addCleanup]);

  return <div>组件内容</div>;
};
```

#### 4.2 对象池模式
```typescript
// 便签对象池
class StickyNotePool {
  private static pool: StickyNote[] = [];
  private static readonly MAX_POOL_SIZE = 100;

  // 获取便签对象
  static acquire(): StickyNote {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    
    return this.createNew();
  }

  // 释放便签对象
  static release(note: StickyNote): void {
    if (this.pool.length < this.MAX_POOL_SIZE) {
      // 重置对象状态
      this.resetNote(note);
      this.pool.push(note);
    }
  }

  private static createNew(): StickyNote {
    return {
      id: '',
      x: 0,
      y: 0,
      width: 200,
      height: 150,
      content: '',
      title: '',
      color: 'yellow',
      isEditing: false,
      isTitleEditing: false,
      isNew: true,
      zIndex: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private static resetNote(note: StickyNote): void {
    note.id = '';
    note.x = 0;
    note.y = 0;
    note.width = 200;
    note.height = 150;
    note.content = '';
    note.title = '';
    note.color = 'yellow';
    note.isEditing = false;
    note.isTitleEditing = false;
    note.isNew = true;
    note.zIndex = 0;
  }
}
```

---

通过实施这些性能优化策略和最佳实践，可以显著提升 AI 便签应用的性能和用户体验。建议根据实际使用情况逐步实施这些优化措施，并持续监控性能指标以确保优化效果。
