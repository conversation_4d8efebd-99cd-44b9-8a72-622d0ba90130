/* 搜索模态框样式 */
.search-modal .ant-modal-content {
  border-radius: 12px;
}

.search-modal-header {
  display: flex;
  align-items: center;
}

.search-modal-content {
  padding: 0;
}

/* 搜索输入框 */
.search-input-container {
  margin-bottom: 16px;
}

.search-input-container .ant-input-affix-wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-container .ant-input-affix-wrapper:focus,
.search-input-container .ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 过滤器区域 */
.search-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group .ant-btn-group {
  margin-left: 8px;
}

/* 搜索结果 */
.search-results {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.search-result-item {
  cursor: pointer;
  padding: 12px 16px !important;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
}

.search-result-item:hover {
  background-color: #f9f9f9;
}

.search-result-item:last-child {
  border-bottom: none;
}

.note-title {
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-content {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

/* 搜索高亮 */
.search-highlight {
  background: #fffb00;
  color: #000;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* 滚动条样式 */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-group {
    justify-content: center;
  }

  .search-results {
    max-height: 300px;
  }
}
