.sticky-note {
  position: absolute;
  border: none; /* 去掉边线 */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 20px rgba(0, 0, 0, 0.06); /* 更柔和的双层阴影 */
  user-select: none;
  transform-origin: top left;
  min-width: 200px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s ease;
  font-size: 14px; /* 基础字体大小 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter", "Arial",
    sans-serif; /* 跨平台优质字体栈 */

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* 便签颜色变体 */
.sticky-note.color-yellow {
  background: #fef3c7;
}

.sticky-note.color-blue {
  background: #dbeafe;
}

.sticky-note.color-green {
  background: #d1fae5;
}

.sticky-note.color-pink {
  background: #fce7f3;
}

.sticky-note.color-purple {
  background: #e9d5ff;
}

.sticky-note:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 8px 32px rgba(0, 0, 0, 0.08); /* 悬停时增强阴影 */
}

.sticky-note.dragging {
  cursor: grabbing;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 16px 64px rgba(0, 0, 0, 0.15); /* 拖动时更强的阴影 */
  transform: scale(1.02); /* 轻微放大增强拖动感 */
}

.sticky-note.editing {
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15),
    0 8px 32px rgba(59, 130, 246, 0.1), 0 0 0 2px rgba(59, 130, 246, 0.2); /* 编辑时蓝色阴影+焦点环 */
}

.sticky-note.new {
  animation: slideInBounce 0.5s ease-out;
}

@keyframes slideInBounce {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(10deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) rotate(-2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.sticky-note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  /* 移除独立背景色 */
  /* background: rgba(0, 0, 0, 0.02); */
  /* 移除分割线 */
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.08); */
  border-radius: 8px 8px 0 0;
  /* 更新flex布局 */
  flex-wrap: nowrap;
}

.sticky-note-title {
  margin: 0;
  font-size: 16px; /* 增大字号 */
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  /* 不再使用flex: 1 */
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* 移除文本居中 */
}

/* 移除悬停效果，因为背景色现在是永久显示的 */
/* .sticky-note-title:hover {
  background: rgba(0, 0, 0, 0.05);
} */

.sticky-note-controls {
  display: flex;
  gap: 4px;
  opacity: 0; /* 恢复透明度控制，默认隐藏 */
  transition: opacity 0.2s ease;
}

/* 恢复鼠标悬浮才显示控件的规则 */
.sticky-note:hover .sticky-note-controls {
  opacity: 1;
}

/* 编辑状态下始终显示控件 */
.sticky-note.editing .sticky-note-controls {
  opacity: 1;
}

.sticky-note-controls button {
  background: rgba(0, 0, 0, 0.06); /* 与标题背景色一致 */
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.sticky-note-controls button:hover {
  background: rgba(0, 0, 0, 0.1); /* 悬浮时稍微深一点 */
}

.sticky-note-controls button.delete-button:hover {
  color: #ff4d4f !important; /* 悬浮时图标变为红色 */
  background: rgba(255, 77, 79, 0.1); /* 悬浮时背景带有淡红色 */
}

.sticky-note-content {
  flex: 1;
  padding-left: 16px; /* 明确左内边距 */
  padding-right: 16px; /* 明确右内边距 */
  padding-top: 16px; /* 保持上内边距 */
  padding-bottom: 16px; /* 保持下内边距 */
  overflow: hidden; /* Re-add to clip the oversized child */
  display: flex;
  flex-direction: column;
}

.sticky-note-textarea {
  width: calc(100% + 17px); /* Oversize to hide scrollbar in overflow */
  flex: 1; /* Use flex to grow like preview mode */
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter", "Arial",
    sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  box-sizing: border-box;
  padding-left: 0; /* Ensure no default textarea left padding */
  /* padding-right: 17px; -- Removed as per user feedback */
  margin-right: -17px; /* Pull scrollbar area into the overflow hidden area */
  overflow-y: scroll;
}

.sticky-note-textarea::placeholder {
  color: #9ca3af;
}

.sticky-note-preview {
  flex: 1;
  overflow-y: scroll; /* 始终显示滚动条轨道以占据空间 */
  cursor: text;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter", "Arial",
    sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  box-sizing: border-box;
  /* padding-left: 0; -- Redundant, already set or implicitly 0 for div */
  width: calc(100% + 17px); /* Oversize to hide scrollbar in overflow */
  padding-left: 0; /* Ensure consistent left padding */
  /* padding-right: 17px; -- Removed as per user feedback */
  margin-right: -17px; /* Pull scrollbar area into the overflow hidden area */
}

.sticky-note-preview:hover {
  background: none;
  border-radius: 4px;
}

.empty-note {
  color: #374151;
  font-style: normal;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  /* 移除背景使其透明 */
  background: transparent;
  /* 始终保持透明 */
  opacity: 0;
  /* 移除过渡效果，因为它不再可见 */
  /* transition: opacity 0.2s ease; */
}

/* 移除悬停效果，因为手柄现在是不可见的 */
/* .sticky-note:hover .resize-handle {
  opacity: 0.6;
} */

/* .resize-handle:hover {
  opacity: 1;
} */

/* Markdown 样式 */
.sticky-note-preview h1,
.sticky-note-preview h2,
.sticky-note-preview h3,
.sticky-note-preview h4,
.sticky-note-preview h5,
.sticky-note-preview h6 {
  margin-top: 0;
  margin-bottom: 8px;
  font-weight: 600;
}

.sticky-note-preview h1 {
  font-size: 18px;
}
.sticky-note-preview h2 {
  font-size: 16px;
}
.sticky-note-preview h3 {
  font-size: 15px;
}
.sticky-note-preview h4 {
  font-size: 14px;
}
.sticky-note-preview h5 {
  font-size: 13px;
}
.sticky-note-preview h6 {
  font-size: 12px;
}

.sticky-note-preview p {
  margin-top: 0;
  margin-bottom: 8px;
}

.sticky-note-preview ul,
.sticky-note-preview ol {
  margin-top: 0;
  margin-bottom: 8px;
  padding-left: 20px;
}

.sticky-note-preview li {
  margin-bottom: 2px;
}

.sticky-note-preview code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SF Mono", "Monaco", "Menlo", "Roboto Mono", "Consolas",
    "Liberation Mono", "Courier New", monospace;
  font-size: 12px;
}

.sticky-note-preview pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

.sticky-note-preview pre code {
  background: none;
  padding: 0;
}

.sticky-note-preview blockquote {
  border-left: 3px solid currentColor;
  margin: 8px 0;
  padding-left: 12px;
  color: #6b7280;
  opacity: 0.8;
}

.sticky-note-preview table {
  border-collapse: collapse;
  width: 100%;
  font-size: 12px;
  margin: 8px 0;
}

.sticky-note-preview th,
.sticky-note-preview td {
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  text-align: left;
}

.sticky-note-preview th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.sticky-note-preview a {
  color: #3b82f6;
  text-decoration: none;
}

.sticky-note-preview a:hover {
  text-decoration: underline;
}

.sticky-note-preview strong {
  font-weight: 600;
}

.sticky-note-preview em {
  font-style: italic;
}

.sticky-note-preview del {
  text-decoration: line-through;
}

/* 响应式设计 - 在小缩放级别下调整字体 */
@media (max-width: 768px) {
  .sticky-note {
    font-size: 12px;
  }

  .sticky-note-preview h1 {
    font-size: 16px;
  }
  .sticky-note-preview h2 {
    font-size: 14px;
  }
  .sticky-note-preview h3 {
    font-size: 13px;
  }
}

/* 滚动条样式，使其不挤占内容空间 */
/*
.sticky-note-preview,
.sticky-note-textarea {
  scrollbar-width: auto; 
  scrollbar-color: transparent transparent; 
}

.sticky-note-preview::-webkit-scrollbar,
.sticky-note-textarea::-webkit-scrollbar {
  width: 17px; 
  height: 17px;
}

.sticky-note-preview::-webkit-scrollbar-track,
.sticky-note-textarea::-webkit-scrollbar-track {
  background: transparent; 
}

.sticky-note-preview::-webkit-scrollbar-thumb,
.sticky-note-textarea::-webkit-scrollbar-thumb {
  background-color: transparent; 
  border-radius: 4px;
  border: 4px solid transparent; 
  background-clip: content-box; 
}


.sticky-note:hover .sticky-note-preview,
.sticky-note:hover .sticky-note-textarea {
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent; 
}

.sticky-note:hover .sticky-note-preview::-webkit-scrollbar-thumb,
.sticky-note:hover .sticky-note-textarea::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15); 
}


.sticky-note:hover .sticky-note-preview,
.sticky-note:hover .sticky-note-textarea {
  
  
}
*/

.sticky-note-title-input {
  margin: 0;
  font-size: 16px; /* 增大字号，与标题一致 */
  font-weight: 600;
  color: #374151;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 6px;
  outline: none;
  font-family: inherit;
  /* 移除文本居中 */
  width: auto;
  min-width: 100px;
}

.sticky-note-title-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}
