import React, { useState } from "react";
import {
  Modal,
  Tabs,
  Form,
  Switch,
  Select,
  AutoComplete,
  Slider,
  ColorPicker,
  Divider,
  Space,
  Typography,
  Card,
  Radio,
  InputNumber,
  Button,
  Input,
  message,
  Spin,
  Alert,
} from "antd";
import {
  UserOutlined,
  SettingOutlined,
  SkinOutlined,
  SafetyOutlined,
  BellOutlined,
  InfoCircleOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { useAISettings } from "../hooks/useAISettings";
import ModelPerformanceIndicator from "./ModelPerformanceIndicator";
import "./SettingsModal.css";

const { Title, Text } = Typography;
const { Option } = Select;

interface SettingsModalProps {
  open: boolean;
  onCancel: () => void;
  defaultActiveTab?: string; // 新增：默认激活的标签页
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  open,
  onCancel,
  defaultActiveTab = "general",
}) => {
  const [form] = Form.useForm();
  const [aiForm] = Form.useForm();
  const [appearanceForm] = Form.useForm();
  const [dataForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [testingConnection, setTestingConnection] = useState(false);
  const [recentModels, setRecentModels] = useState<string[]>([]);
  const [currentModelInput, setCurrentModelInput] = useState<string>("");

  // 获取最近使用的模型
  React.useEffect(() => {
    const getRecentModels = () => {
      try {
        const saved = localStorage.getItem("recent-ai-models");
        if (saved) {
          setRecentModels(JSON.parse(saved));
        }
      } catch (error) {
        console.warn("获取最近使用模型失败:", error);
      }
    };
    getRecentModels();
  }, []);

  // 添加到最近使用模型
  const addToRecentModels = (modelName: string) => {
    if (!modelName) return;

    setRecentModels((prev) => {
      const updated = [modelName, ...prev.filter((m) => m !== modelName)].slice(
        0,
        5
      );
      try {
        localStorage.setItem("recent-ai-models", JSON.stringify(updated));
      } catch (error) {
        console.warn("保存最近使用模型失败:", error);
      }
      return updated;
    });
  };

  // 验证模型名称
  const validateModelName = (
    modelName: string
  ): { isValid: boolean; suggestion?: string } => {
    if (!modelName) return { isValid: false };

    // 检查是否是已知的模型
    const knownModel = aiModelOptions.find((opt) => opt.value === modelName);
    if (knownModel) return { isValid: true };

    // 检查常见的模型名称模式
    const commonPatterns = [
      /^gpt-[34](\.\d+)?(-turbo)?$/i,
      /^claude-[23]-(haiku|sonnet|opus)$/i,
      /^deepseek-(chat|coder)$/i,
      /^llama-?\d+b?$/i,
      /^gemini-pro$/i,
      /^mistral-\d+b$/i,
    ];

    const isValidPattern = commonPatterns.some((pattern) =>
      pattern.test(modelName)
    );

    if (!isValidPattern) {
      // 提供建议
      if (modelName.toLowerCase().includes("gpt")) {
        return {
          isValid: true,
          suggestion: "建议使用: gpt-3.5-turbo 或 gpt-4",
        };
      } else if (modelName.toLowerCase().includes("claude")) {
        return {
          isValid: true,
          suggestion: "建议使用: claude-3-haiku 或 claude-3-sonnet",
        };
      } else if (modelName.toLowerCase().includes("deepseek")) {
        return {
          isValid: true,
          suggestion: "建议使用: deepseek-chat 或 deepseek-coder",
        };
      }
    }

    return { isValid: true };
  };

  // AI模型选项 - 增强版本，包含模型描述和分类
  const aiModelOptions = [
    // DeepSeek 系列
    {
      value: "deepseek-chat",
      label: "DeepSeek Chat",
      category: "DeepSeek",
      description: "通用对话模型，性价比高",
    },
    {
      value: "deepseek-coder",
      label: "DeepSeek Coder",
      category: "DeepSeek",
      description: "代码专用模型，编程任务优秀",
    },
    // OpenAI GPT 系列
    {
      value: "gpt-3.5-turbo",
      label: "GPT-3.5 Turbo",
      category: "OpenAI",
      description: "快速响应，成本较低",
    },
    {
      value: "gpt-4",
      label: "GPT-4",
      category: "OpenAI",
      description: "高质量输出，理解能力强",
    },
    {
      value: "gpt-4-turbo",
      label: "GPT-4 Turbo",
      category: "OpenAI",
      description: "更快的GPT-4版本",
    },
    {
      value: "gpt-4o",
      label: "GPT-4o",
      category: "OpenAI",
      description: "最新多模态模型",
    },
    // Claude 系列
    {
      value: "claude-3-haiku",
      label: "Claude 3 Haiku",
      category: "Anthropic",
      description: "快速轻量，适合简单任务",
    },
    {
      value: "claude-3-sonnet",
      label: "Claude 3 Sonnet",
      category: "Anthropic",
      description: "平衡性能与成本",
    },
    {
      value: "claude-3-opus",
      label: "Claude 3 Opus",
      category: "Anthropic",
      description: "最强推理能力",
    },
    // 其他模型
    {
      value: "gemini-pro",
      label: "Gemini Pro",
      category: "Google",
      description: "Google最新模型",
    },
    {
      value: "llama-2-7b",
      label: "Llama 2 7B",
      category: "Meta",
      description: "开源模型，轻量快速",
    },
    {
      value: "llama-2-13b",
      label: "Llama 2 13B",
      category: "Meta",
      description: "中等规模，性能均衡",
    },
    {
      value: "llama-2-70b",
      label: "Llama 2 70B",
      category: "Meta",
      description: "大规模模型，能力强",
    },
    {
      value: "code-llama",
      label: "Code Llama",
      category: "Meta",
      description: "代码生成专用模型",
    },
    {
      value: "mistral-7b",
      label: "Mistral 7B",
      category: "Mistral",
      description: "欧洲开源模型",
    },
    {
      value: "mixtral-8x7b",
      label: "Mixtral 8x7B",
      category: "Mistral",
      description: "混合专家模型",
    },
  ];

  const {
    config: aiConfig,
    loading: aiLoading,
    error: aiError,
    saveConfig: saveAIConfig,
    testConnection,
    hasValidConfig,
  } = useAISettings();

  // 当aiConfig变化时，更新表单的值（只在模态框打开时）
  React.useEffect(() => {
    console.log("🎛️ SettingsModal: AI配置变化", { open, aiConfig });

    if (open && aiConfig) {
      // 只有当配置不是默认空配置时才更新表单值
      const hasValidData =
        aiConfig.apiKey ||
        aiConfig.enableAI ||
        aiConfig.aiModel !== "deepseek-chat" ||
        aiConfig.apiUrl !== "https://api.deepseek.com/v1";

      if (hasValidData) {
        console.log("🎛️ SettingsModal: 更新AI表单值", aiConfig);
        try {
          aiForm.setFieldsValue(aiConfig);
          console.log("🎛️ SettingsModal: AI表单值已更新");
        } catch (error) {
          console.warn("🎛️ SettingsModal: 更新表单值失败", error);
        }
      }
    }
  }, [aiConfig, open, aiForm]);

  // 测试AI连接
  const handleTestConnection = async () => {
    try {
      setTestingConnection(true);
      await aiForm.validateFields();

      const result = await testConnection();

      if (result.success) {
        message.success("连接测试成功！");
      } else {
        message.error(`连接测试失败: ${result.error}`);
      }
    } catch (error) {
      message.error("请先完善配置信息");
    } finally {
      setTestingConnection(false);
    }
  };

  // 保存AI配置
  const handleSaveAIConfig = async () => {
    console.log("🎛️ SettingsModal: 用户点击保存AI配置");

    try {
      const values = await aiForm.validateFields();
      console.log("🎛️ SettingsModal: 表单验证通过，获取的值", values);

      const configToSave = { ...aiConfig, ...values };
      console.log("🎛️ SettingsModal: 准备保存的完整配置", configToSave);

      const success = await saveAIConfig(configToSave);

      if (success) {
        console.log("🎛️ SettingsModal: AI配置保存成功");
        message.success("AI配置保存成功！");
      } else {
        console.error("🎛️ SettingsModal: AI配置保存失败");
      }
    } catch (error) {
      console.error("🎛️ SettingsModal: 表单验证失败或保存异常", error);
      message.error("请检查配置信息");
    }
  };

  const tabItems = [
    {
      key: "general",
      label: (
        <span>
          <SettingOutlined />
          常规设置
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Form
            key="general-form"
            form={form}
            layout="vertical"
            initialValues={{
              autoSave: true,
              language: "zh-CN",
              theme: "light",
              autoBackup: true,
              saveInterval: 30,
              username: "用户名称",
              email: "<EMAIL>",
            }}
          >
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                <UserOutlined style={{ marginRight: 8 }} />
                个人信息
              </Title>
              <Form.Item label="用户名" name="username">
                <Select style={{ width: "100%" }}>
                  <Option value="用户名称">用户名称</Option>
                </Select>
              </Form.Item>
              <Form.Item label="邮箱" name="email">
                <Select style={{ width: "100%" }}>
                  <Option value="<EMAIL>"><EMAIL></Option>
                </Select>
              </Form.Item>
            </Card>

            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                应用设置
              </Title>
              <Form.Item
                label="语言设置"
                name="language"
                extra="更改语言需要重启应用"
              >
                <Select style={{ width: "100%" }}>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                  <Option value="ja-JP">日本語</Option>
                </Select>
              </Form.Item>

              <Form.Item label="主题模式" name="theme">
                <Radio.Group>
                  <Radio value="light">浅色模式</Radio>
                  <Radio value="dark">深色模式</Radio>
                  <Radio value="auto">跟随系统</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="自动保存"
                name="autoSave"
                valuePropName="checked"
                extra="实时保存您的便签内容"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="保存间隔（秒）"
                name="saveInterval"
                extra="自动保存的时间间隔"
              >
                <Slider
                  min={10}
                  max={300}
                  marks={{
                    10: "10s",
                    60: "1min",
                    180: "3min",
                    300: "5min",
                  }}
                />
              </Form.Item>
            </Card>
          </Form>
        </div>
      ),
    },
    {
      key: "appearance",
      label: (
        <span>
          <SkinOutlined />
          外观设置
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Form
            form={appearanceForm}
            layout="vertical"
            initialValues={{
              canvasBackground: "#ffffff",
              gridVisible: true,
              gridSize: 20,
              noteDefaultColor: "#fef3c7",
              fontSize: 14,
              fontFamily: "system-ui",
            }}
          >
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                画布设置
              </Title>
              <Form.Item label="画布背景色" name="canvasBackground">
                <ColorPicker showText />
              </Form.Item>

              <Form.Item
                label="显示网格"
                name="gridVisible"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item label="网格大小" name="gridSize">
                <Slider
                  min={10}
                  max={50}
                  marks={{
                    10: "10px",
                    20: "20px",
                    30: "30px",
                    50: "50px",
                  }}
                />
              </Form.Item>
            </Card>

            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                便签样式
              </Title>
              <Form.Item label="默认便签颜色" name="noteDefaultColor">
                <ColorPicker
                  presets={[
                    {
                      label: "常用颜色",
                      colors: [
                        "#fef3c7", // yellow
                        "#dbeafe", // blue
                        "#d1fae5", // green
                        "#fce7f3", // pink
                        "#e9d5ff", // purple
                      ],
                    },
                  ]}
                  showText
                />
              </Form.Item>

              <Form.Item label="字体大小" name="fontSize">
                <InputNumber
                  min={12}
                  max={24}
                  suffix="px"
                  style={{ width: "100%" }}
                />
              </Form.Item>

              <Form.Item label="字体系列" name="fontFamily">
                <Select>
                  <Option value="system-ui">系统默认</Option>
                  <Option value="Arial">Arial</Option>
                  <Option value="Microsoft YaHei">微软雅黑</Option>
                  <Option value="PingFang SC">苹方</Option>
                </Select>
              </Form.Item>
            </Card>
          </Form>
        </div>
      ),
    },
    {
      key: "data",
      label: (
        <span>
          <SafetyOutlined />
          数据管理
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Form
            form={dataForm}
            layout="vertical"
            initialValues={{
              autoBackup: true,
              backupFrequency: "daily",
              maxBackups: 10,
            }}
          >
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                备份设置
              </Title>
              <Form.Item
                label="自动备份"
                name="autoBackup"
                valuePropName="checked"
                extra="定期自动备份您的数据"
              >
                <Switch />
              </Form.Item>

              <Form.Item label="备份频率" name="backupFrequency">
                <Select>
                  <Option value="realtime">实时备份</Option>
                  <Option value="daily">每日备份</Option>
                  <Option value="weekly">每周备份</Option>
                  <Option value="monthly">每月备份</Option>
                </Select>
              </Form.Item>

              <Form.Item label="最大备份数量" name="maxBackups">
                <InputNumber min={1} max={50} style={{ width: "100%" }} />
              </Form.Item>
            </Card>

            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                数据操作
              </Title>
              <Space direction="vertical" style={{ width: "100%" }}>
                <Button type="primary" ghost style={{ width: "100%" }}>
                  导出所有数据
                </Button>
                <Button style={{ width: "100%" }}>导入数据</Button>
                <Divider />
                <Button danger style={{ width: "100%" }}>
                  清空所有数据
                </Button>
              </Space>
              <Text
                type="secondary"
                style={{ fontSize: 12, marginTop: 8, display: "block" }}
              >
                ⚠️ 清空数据操作不可恢复，请谨慎操作
              </Text>
            </Card>
          </Form>
        </div>
      ),
    },
    {
      key: "ai",
      label: (
        <span>
          <RobotOutlined />
          AI设置
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Spin spinning={aiLoading}>
            {aiError && (
              <Alert
                message="配置错误"
                description={aiError}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            {!aiError && !hasValidConfig && (
              <Alert
                message="AI 配置不完整"
                description="请检查并完善API密钥、API地址等AI配置项以启用全部AI功能。"
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            <Form
              form={aiForm}
              layout="vertical"
              onFinish={handleSaveAIConfig}
              preserve={true}
              initialValues={
                aiConfig || {
                  enableAI: false,
                  aiModel: "deepseek-chat",
                  apiKey: "",
                  apiUrl: "https://api.deepseek.com/v1",
                  temperature: 0.7,
                  maxTokens: 1000,
                }
              }
            >
              <Card size="small" style={{ marginBottom: 16 }}>
                <Title level={5} style={{ margin: "0 0 16px 0" }}>
                  <RobotOutlined style={{ marginRight: 8 }} />
                  AI模型配置
                </Title>
                <Form.Item
                  label="启用AI功能"
                  name="enableAI"
                  valuePropName="checked"
                  extra="开启后可使用AI生成便签功能"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  label="AI模型"
                  name="aiModel"
                  extra={
                    <div>
                      <div>选择或输入要使用的AI模型名称</div>
                      {currentModelInput &&
                        (() => {
                          const validation =
                            validateModelName(currentModelInput);
                          return validation.suggestion ? (
                            <div
                              style={{
                                color: "#faad14",
                                fontSize: "12px",
                                marginTop: "4px",
                              }}
                            >
                              💡 {validation.suggestion}
                            </div>
                          ) : null;
                        })()}
                    </div>
                  }
                  rules={[{ required: true, message: "请选择或输入AI模型" }]}
                >
                  <AutoComplete
                    style={{ width: "100%" }}
                    placeholder="输入或选择AI模型"
                    options={[
                      // 最近使用的模型（如果有）
                      ...(recentModels.length > 0
                        ? [
                            {
                              label: "最近使用",
                              options: recentModels.map((model) => ({
                                value: model,
                                label: `${model} (最近使用)`,
                              })),
                            },
                          ]
                        : []),
                      // 推荐模型分组
                      {
                        label: "推荐模型",
                        options: aiModelOptions.filter((opt) =>
                          [
                            "deepseek-chat",
                            "gpt-3.5-turbo",
                            "claude-3-haiku",
                          ].includes(opt.value)
                        ),
                      },
                      // 所有模型分组
                      {
                        label: "所有模型",
                        options: aiModelOptions,
                      },
                    ]}
                    filterOption={(inputValue, option) => {
                      if (
                        option &&
                        typeof option === "object" &&
                        "value" in option &&
                        "label" in option
                      ) {
                        const opt = option as { value: string; label: string };
                        return (
                          opt.value
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1 ||
                          opt.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        );
                      }
                      return false;
                    }}
                    onSelect={(value) => {
                      addToRecentModels(value);
                      setCurrentModelInput(value);
                    }}
                    onSearch={(value) => {
                      setCurrentModelInput(value);
                    }}
                    allowClear
                  />
                </Form.Item>

                {/* 显示模型性能信息 */}
                <Form.Item
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.aiModel !== currentValues.aiModel
                  }
                >
                  {({ getFieldValue }) => {
                    const selectedModel = getFieldValue("aiModel");
                    if (!selectedModel) return null;

                    return (
                      <div style={{ marginTop: "-16px", marginBottom: "16px" }}>
                        <ModelPerformanceIndicator
                          modelName={selectedModel}
                          showDetails={true}
                        />
                      </div>
                    );
                  }}
                </Form.Item>

                <Form.Item
                  label="API密钥"
                  name="apiKey"
                  extra="请输入您的AI服务API密钥"
                  rules={[
                    { required: true, message: "请输入API密钥" },
                    { min: 10, message: "API密钥长度不能少于10个字符" },
                  ]}
                >
                  <Input.Password
                    placeholder="sk-..."
                    style={{ width: "100%" }}
                    visibilityToggle={false}
                  />
                </Form.Item>

                <Form.Item
                  label="API地址"
                  name="apiUrl"
                  extra="API服务的基础URL地址"
                  rules={[
                    { required: true, message: "请输入API地址" },
                    { type: "url", message: "请输入有效的URL地址" },
                  ]}
                >
                  <Input
                    placeholder="https://api.deepseek.com/v1"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
              </Card>

              <Card size="small" style={{ marginBottom: 16 }}>
                <Title level={5} style={{ margin: "0 0 16px 0" }}>
                  模型参数
                </Title>
                <Form.Item
                  label="温度值"
                  name="temperature"
                  extra="控制生成内容的随机性，0-1之间，值越高越随机"
                >
                  <Slider
                    min={0}
                    max={1}
                    step={0.1}
                    marks={{
                      0: "精确",
                      0.5: "平衡",
                      1: "创意",
                    }}
                    tooltip={{ formatter: (value) => `${value}` }}
                  />
                </Form.Item>

                <Form.Item
                  label="最大生成令牌数"
                  name="maxTokens"
                  extra="控制生成内容的最大长度"
                >
                  <Slider
                    min={50}
                    max={4000}
                    step={50}
                    marks={{
                      50: "简短",
                      1000: "适中",
                      4000: "详细",
                    }}
                    tooltip={{ formatter: (value) => `${value}` }}
                  />
                </Form.Item>
              </Card>

              <div className="form-actions">
                <Space>
                  <Button
                    type="primary"
                    onClick={handleTestConnection}
                    loading={testingConnection}
                    disabled={aiLoading}
                  >
                    测试连接
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleSaveAIConfig}
                    disabled={aiLoading}
                  >
                    保存配置
                  </Button>
                </Space>
              </div>
            </Form>
          </Spin>
        </div>
      ),
    },
    {
      key: "notifications",
      label: (
        <span>
          <BellOutlined />
          通知设置
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Form
            form={notificationForm}
            layout="vertical"
            initialValues={{
              enableNotifications: true,
              notifyOnSync: true,
              notifyOnBackup: true,
              notifyOnShare: true,
              soundEnabled: true,
              notificationSound: "default",
            }}
          >
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                通知选项
              </Title>
              <Form.Item
                label="启用通知"
                name="enableNotifications"
                valuePropName="checked"
                extra="允许应用发送系统通知"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="同步通知"
                name="notifyOnSync"
                valuePropName="checked"
                extra="数据同步完成时通知"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="备份通知"
                name="notifyOnBackup"
                valuePropName="checked"
                extra="自动备份完成时通知"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label="分享通知"
                name="notifyOnShare"
                valuePropName="checked"
                extra="内容被分享时通知"
              >
                <Switch />
              </Form.Item>
            </Card>

            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5} style={{ margin: "0 0 16px 0" }}>
                声音设置
              </Title>
              <Form.Item
                label="启用提示音"
                name="soundEnabled"
                valuePropName="checked"
                extra="操作时播放提示音"
              >
                <Switch />
              </Form.Item>

              <Form.Item label="提示音选择" name="notificationSound">
                <Select>
                  <Option value="default">默认提示音</Option>
                  <Option value="chime">清脆提示音</Option>
                  <Option value="bell">铃声提示音</Option>
                  <Option value="none">静音</Option>
                </Select>
              </Form.Item>
            </Card>
          </Form>
        </div>
      ),
    },
    {
      key: "about",
      label: (
        <span>
          <InfoCircleOutlined />
          关于
        </span>
      ),
      children: (
        <div className="settings-modal-content">
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5} style={{ margin: "0 0 16px 0" }}>
              应用信息
            </Title>
            <p>
              <strong>便签画布</strong>{" "}
              是一款创新的无限画布便签应用，让您自由组织思路和灵感。
            </p>
            <p>版本: 1.0.0</p>
            <Divider />
            <p>
              <strong>开发者:</strong> 便签画布团队
            </p>
            <p>
              <strong>联系我们:</strong> <EMAIL>
            </p>
            <Divider />
            <p>© 2023 便签画布. 保留所有权利.</p>
          </Card>
        </div>
      ),
    },
  ];

  return (
    <Modal
      title="设置"
      open={open}
      onCancel={onCancel}
      width={720}
      centered
      styles={{ body: { height: "60vh", overflowY: "hidden" } }}
      footer={null}
      destroyOnHidden
      className="settings-modal"
    >
      <Tabs
        defaultActiveKey={defaultActiveTab}
        items={tabItems}
        tabPosition="left"
        className="settings-tabs"
        style={{ height: "100%" }}
      />
    </Modal>
  );
};

export default SettingsModal;
