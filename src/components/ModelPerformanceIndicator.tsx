import React from "react";
import { Tag, Tooltip } from "antd";
import {
  <PERSON>boltOutlined,
  DollarOutlined,
  BulbOutlined,
  CodeOutlined,
  GlobalOutlined,
} from "@ant-design/icons";

interface ModelInfo {
  speed: number; // 1-5 速度评分
  cost: number; // 1-5 成本评分 (1=便宜, 5=昂贵)
  intelligence: number; // 1-5 智能评分
  coding: boolean; // 是否适合编程
  multimodal: boolean; // 是否支持多模态
  context: number; // 上下文长度 (K tokens)
  description: string;
}

const modelPerformanceData: Record<string, ModelInfo> = {
  "deepseek-chat": {
    speed: 4,
    cost: 1,
    intelligence: 4,
    coding: false,
    multimodal: false,
    context: 32,
    description: "性价比极高的通用对话模型",
  },
  "deepseek-coder": {
    speed: 4,
    cost: 1,
    intelligence: 4,
    coding: true,
    multimodal: false,
    context: 16,
    description: "专为编程优化的代码生成模型",
  },
  "gpt-3.5-turbo": {
    speed: 5,
    cost: 2,
    intelligence: 3,
    coding: false,
    multimodal: false,
    context: 16,
    description: "快速响应，适合一般对话任务",
  },
  "gpt-4": {
    speed: 3,
    cost: 4,
    intelligence: 5,
    coding: true,
    multimodal: false,
    context: 8,
    description: "高质量输出，理解能力最强",
  },
  "gpt-4-turbo": {
    speed: 4,
    cost: 4,
    intelligence: 5,
    coding: true,
    multimodal: true,
    context: 128,
    description: "更快的GPT-4，支持更长上下文",
  },
  "gpt-4o": {
    speed: 4,
    cost: 3,
    intelligence: 5,
    coding: true,
    multimodal: true,
    context: 128,
    description: "最新多模态模型，平衡性能与成本",
  },
  "claude-3-haiku": {
    speed: 5,
    cost: 1,
    intelligence: 3,
    coding: false,
    multimodal: false,
    context: 200,
    description: "快速轻量，适合简单任务",
  },
  "claude-3-sonnet": {
    speed: 4,
    cost: 3,
    intelligence: 4,
    coding: true,
    multimodal: true,
    context: 200,
    description: "平衡性能与成本的中等模型",
  },
  "claude-3-opus": {
    speed: 3,
    cost: 5,
    intelligence: 5,
    coding: true,
    multimodal: true,
    context: 200,
    description: "最强推理能力，适合复杂任务",
  },
  "gemini-pro": {
    speed: 4,
    cost: 2,
    intelligence: 4,
    coding: true,
    multimodal: true,
    context: 32,
    description: "Google最新模型，多模态能力强",
  },
};

interface ModelPerformanceIndicatorProps {
  modelName: string;
  showDetails?: boolean;
}

const ModelPerformanceIndicator: React.FC<ModelPerformanceIndicatorProps> = ({
  modelName,
  showDetails = false,
}) => {
  const modelInfo = modelPerformanceData[modelName];

  if (!modelInfo) {
    return (
      <div style={{ fontSize: "12px", color: "#666" }}>
        自定义模型 - 性能信息不可用
      </div>
    );
  }

  const speedColor =
    modelInfo.speed >= 4
      ? "#52c41a"
      : modelInfo.speed >= 3
      ? "#faad14"
      : "#ff4d4f";
  const costColor =
    modelInfo.cost <= 2
      ? "#52c41a"
      : modelInfo.cost <= 3
      ? "#faad14"
      : "#ff4d4f";
  const intelligenceColor =
    modelInfo.intelligence >= 4
      ? "#52c41a"
      : modelInfo.intelligence >= 3
      ? "#faad14"
      : "#ff4d4f";

  if (!showDetails) {
    return (
      <div style={{ fontSize: "12px", color: "#666", marginTop: "4px" }}>
        {modelInfo.description}
      </div>
    );
  }

  return (
    <div
      style={{
        marginTop: "8px",
        padding: "8px",
        backgroundColor: "#fafafa",
        borderRadius: "4px",
      }}
    >
      <div style={{ marginBottom: "8px", fontSize: "12px", color: "#666" }}>
        {modelInfo.description}
      </div>

      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          gap: "8px",
          marginBottom: "8px",
        }}
      >
        <Tooltip title={`速度评分: ${modelInfo.speed}/5`}>
          <Tag icon={<ThunderboltOutlined />} color={speedColor}>
            速度 {modelInfo.speed}/5
          </Tag>
        </Tooltip>

        <Tooltip title={`成本评分: ${modelInfo.cost}/5 (分数越低越便宜)`}>
          <Tag icon={<DollarOutlined />} color={costColor}>
            成本 {modelInfo.cost}/5
          </Tag>
        </Tooltip>

        <Tooltip title={`智能评分: ${modelInfo.intelligence}/5`}>
          <Tag icon={<BulbOutlined />} color={intelligenceColor}>
            智能 {modelInfo.intelligence}/5
          </Tag>
        </Tooltip>

        {modelInfo.coding && (
          <Tag icon={<CodeOutlined />} color="blue">
            编程优化
          </Tag>
        )}

        {modelInfo.multimodal && (
          <Tag icon={<GlobalOutlined />} color="purple">
            多模态
          </Tag>
        )}
      </div>

      <div style={{ fontSize: "11px", color: "#999" }}>
        上下文长度: {modelInfo.context}K tokens
      </div>
    </div>
  );
};

export default ModelPerformanceIndicator;
