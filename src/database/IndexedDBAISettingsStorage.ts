// 使用 IndexedDB 存储 AI 设置
import { type AIConfig, defaultAIConfig } from "../services/aiService";
import { IndexedDBService } from "./IndexedDBService";

// 临时保留本地存储版本，便于数据迁移
import { AISettingsStorage as LocalStorageAISettingsStorage } from "../services/aiSettingsStorage";

// 定义存储在 IndexedDB 中的 AI 设置结构
interface StoredAISettings extends AIConfig {
  id: string;
  user_id: string;
  updated_at: string;
}

export class IndexedDBAISettingsStorage {
  private static readonly DEFAULT_USER_ID = "default";

  // 保存AI配置到IndexedDB，添加自动修正逻辑
  static async saveConfig(config: AIConfig): Promise<void> {
    console.log("💾 IndexedDBAISettingsStorage: 开始保存配置", config);

    try {
      // 验证配置，并获取可能的修正版本
      const validation = this.validateConfig(config);

      // 如果配置有错误但可以修正，使用修正后的版本
      let configToUse: AIConfig = { ...config };
      if (!validation.isValid && validation.correctedConfig) {
        console.warn("检测到无效的AI配置参数，已自动修正:", validation.errors);
        // 合并原始配置和修正后的配置
        configToUse = {
          ...config,
          ...(validation.correctedConfig as Partial<AIConfig>),
        };
      }

      const db = IndexedDBService.getInstance();
      await db.initialize();
      console.log("💾 IndexedDBAISettingsStorage: 数据库初始化完成");

      // 加密API密钥
      const configToSave = {
        id: "ai-settings", // 使用固定ID，方便查询
        user_id: this.DEFAULT_USER_ID, // 将来可支持多用户
        ...configToUse,
        // 出于安全考虑，API密钥单独处理
        apiKey: configToUse.apiKey ? this.encryptKey(configToUse.apiKey) : "",
        updated_at: new Date().toISOString(),
      };

      console.log("💾 IndexedDBAISettingsStorage: 准备保存到ai_settings表", {
        ...configToSave,
        apiKey: configToSave.apiKey ? "******" : "",
      });

      await db.putItem("ai_settings", configToSave);
      console.log("💾 IndexedDBAISettingsStorage: 配置保存成功");

      // 如果使用了修正后的配置，记录日志
      if (configToUse !== config) {
        console.info("已保存修正后的AI配置");
      }
    } catch (error) {
      console.error(
        "💾 IndexedDBAISettingsStorage: 保存AI配置到IndexedDB失败:",
        error
      );
      throw new Error("保存AI配置失败");
    }
  }

  // 从IndexedDB加载AI配置
  static async loadConfig(): Promise<AIConfig> {
    console.log("📥 IndexedDBAISettingsStorage: 开始加载AI配置");

    try {
      const db = IndexedDBService.getInstance();
      await db.initialize();
      console.log("📥 IndexedDBAISettingsStorage: 数据库初始化完成");

      // 尝试从 IndexedDB 获取配置
      const settings = await db.getItem<StoredAISettings>(
        "ai_settings",
        "ai-settings"
      );

      console.log(
        "📥 IndexedDBAISettingsStorage: 从数据库获取的原始数据",
        settings
      );

      // 如果没有找到配置，尝试从localStorage迁移
      if (!settings) {
        console.log(
          "📥 IndexedDBAISettingsStorage: 数据库中无配置，尝试从localStorage迁移"
        );
        return await this.migrateFromLocalStorage();
      }

      // 移除与数据库相关的字段，只保留 AIConfig 相关字段
      const { id, user_id, updated_at, ...configData } = settings;

      const finalConfig = {
        ...defaultAIConfig,
        ...configData,
        // 解密API密钥
        apiKey: configData.apiKey ? this.decryptKey(configData.apiKey) : "",
      };

      console.log("📥 IndexedDBAISettingsStorage: 最终解析的配置", {
        ...finalConfig,
        apiKey: finalConfig.apiKey ? "******" : "",
      });

      return finalConfig;
    } catch (error) {
      console.error(
        "📥 IndexedDBAISettingsStorage: 从IndexedDB加载AI配置失败:",
        error
      );

      // 如果从 IndexedDB 加载失败，尝试从 localStorage 加载
      try {
        const migratedConfig = await this.migrateFromLocalStorage();
        // 记录成功迁移的日志
        console.log("成功从localStorage迁移AI配置", migratedConfig);
        return migratedConfig;
      } catch (migrationError) {
        console.error("从localStorage迁移配置失败:", migrationError);
        // 添加更详细的错误日志
        console.warn("使用默认AI配置，请在设置中重新配置AI");
        return { ...defaultAIConfig };
      }
    }
  }

  // 清除AI配置
  static async clearConfig(): Promise<void> {
    try {
      const db = IndexedDBService.getInstance();
      await db.initialize();
      await db.deleteItem("ai_settings", "ai-settings");

      // 同时清除localStorage中的旧配置
      await LocalStorageAISettingsStorage.clearConfig();
    } catch (error) {
      console.error("清除AI配置失败:", error);
    }
  }

  // 检查是否有有效的AI配置
  static async hasValidConfig(): Promise<boolean> {
    try {
      const config = await this.loadConfig();
      return !!(
        config.enableAI &&
        config.apiKey &&
        config.apiUrl &&
        config.aiModel
      );
    } catch {
      return false;
    }
  }

  // 从localStorage迁移数据到IndexedDB，改进迁移逻辑
  private static async migrateFromLocalStorage(): Promise<AIConfig> {
    try {
      // 从localStorage加载旧配置
      const oldConfig = await LocalStorageAISettingsStorage.loadConfig();

      // 检查是否有任何有意义的配置(包括部分配置)
      const hasApiKey = oldConfig.apiKey && oldConfig.apiKey.trim() !== "";
      const hasApiUrl =
        oldConfig.apiUrl && oldConfig.apiUrl !== defaultAIConfig.apiUrl;
      const hasCustomModel =
        oldConfig.aiModel && oldConfig.aiModel !== defaultAIConfig.aiModel;
      const hasCustomSettings =
        oldConfig.enableAI !== defaultAIConfig.enableAI ||
        oldConfig.temperature !== defaultAIConfig.temperature ||
        oldConfig.maxTokens !== defaultAIConfig.maxTokens;

      const hasAnyCustomConfig =
        hasApiKey || hasApiUrl || hasCustomModel || hasCustomSettings;

      // 如果有任何自定义配置，保存到IndexedDB
      if (hasAnyCustomConfig) {
        // 确保合并默认值和用户配置
        const mergedConfig = {
          ...defaultAIConfig,
          ...oldConfig,
        };

        await this.saveConfig(mergedConfig);
        console.log("成功将AI配置从localStorage迁移到IndexedDB，配置内容:", {
          ...mergedConfig,
          apiKey: hasApiKey ? "******" : "",
        });

        return mergedConfig;
      } else {
        console.log("未发现有效的自定义AI配置，使用默认配置");
        return { ...defaultAIConfig };
      }
    } catch (error) {
      console.error("迁移AI配置失败:", error);
      return { ...defaultAIConfig };
    }
  }

  // 改进的API密钥加密（基于AES-like XOR加密算法）
  // 仍然不是生产级安全加密，但比简单的Base64更安全
  private static encryptKey(key: string): string {
    try {
      // 固定的盐值，实际应用中应当使用动态生成的安全盐值
      const salt = "ai-sticky-notes-secret-salt-2025";
      // 使用XOR加密
      let encrypted = "";
      for (let i = 0; i < key.length; i++) {
        const charCode = key.charCodeAt(i) ^ salt.charCodeAt(i % salt.length);
        encrypted += String.fromCharCode(charCode);
      }
      // 最后再用Base64编码
      return btoa(unescape(encodeURIComponent(encrypted)));
    } catch (error) {
      console.error("加密API密钥失败:", error);
      // 降级处理，返回简单混淆
      return btoa(key + "-fallback");
    }
  }

  // 改进的API密钥解密
  private static decryptKey(encryptedKey: string): string {
    try {
      // 固定的盐值，必须与加密时使用的相同
      const salt = "ai-sticky-notes-secret-salt-2025";
      // 先Base64解码
      const decoded = decodeURIComponent(escape(atob(encryptedKey)));
      // 使用XOR解密
      let decrypted = "";
      for (let i = 0; i < decoded.length; i++) {
        const charCode =
          decoded.charCodeAt(i) ^ salt.charCodeAt(i % salt.length);
        decrypted += String.fromCharCode(charCode);
      }
      return decrypted;
    } catch (error) {
      console.error("解密API密钥失败:", error);
      // 尝试旧版解密方式（向后兼容）
      try {
        const decoded = decodeURIComponent(escape(atob(encryptedKey)));
        if (decoded.includes("ai-sticky-notes")) {
          return decoded.replace("ai-sticky-notes", "");
        }
        // 检查是否是fallback加密
        if (encryptedKey.endsWith("LWZhbGxiYWNr")) {
          // Base64编码的"-fallback"
          return atob(encryptedKey).replace("-fallback", "");
        }
      } catch {}

      return ""; // 解密完全失败，返回空字符串
    }
  }

  // 验证配置格式，添加自动修正功能
  static validateConfig(config: Partial<AIConfig>): {
    isValid: boolean;
    errors: string[];
    correctedConfig?: Partial<AIConfig>; // 新增：返回修正后的配置
  } {
    const errors: string[] = [];
    // 创建修正后的配置副本
    const correctedConfig: Partial<AIConfig> = { ...config };

    if (!config.aiModel) {
      errors.push("请选择AI模型");
    }

    if (!config.apiKey || config.apiKey.trim() === "") {
      errors.push("请输入API密钥");
    }

    if (!config.apiUrl || config.apiUrl.trim() === "") {
      errors.push("请输入API地址");
    } else {
      try {
        new URL(config.apiUrl);
      } catch {
        errors.push("API地址格式不正确");
        // 尝试自动修正URL
        if (config.apiUrl.startsWith("http")) {
          try {
            // 尝试修复常见URL问题，例如缺少/v1等
            let fixedUrl = config.apiUrl;
            if (!fixedUrl.endsWith("/v1") && !fixedUrl.endsWith("/")) {
              fixedUrl += "/v1";
              new URL(fixedUrl); // 验证修复后的URL
              correctedConfig.apiUrl = fixedUrl;
              console.log(`自动修正API地址: ${config.apiUrl} -> ${fixedUrl}`);
            }
          } catch {
            // 如果无法修复，保留原始错误
          }
        }
      }
    }

    // 自动修正温度值
    if (
      typeof config.temperature !== "number" ||
      config.temperature < 0 ||
      config.temperature > 1
    ) {
      errors.push("温度值必须在0-1之间");
      // 自动修正为有效范围
      if (typeof config.temperature === "number") {
        correctedConfig.temperature = Math.min(
          Math.max(config.temperature, 0),
          1
        );
        console.log(
          `自动修正温度值: ${config.temperature} -> ${correctedConfig.temperature}`
        );
      } else {
        correctedConfig.temperature = defaultAIConfig.temperature;
        console.log(`温度值无效，使用默认值: ${defaultAIConfig.temperature}`);
      }
    }

    // 自动修正maxTokens
    if (
      typeof config.maxTokens !== "number" ||
      config.maxTokens < 1 ||
      config.maxTokens > 4000
    ) {
      errors.push("最大Token数必须在1-4000之间");
      // 自动修正为有效范围
      if (typeof config.maxTokens === "number") {
        correctedConfig.maxTokens = Math.min(
          Math.max(config.maxTokens, 1),
          4000
        );
        console.log(
          `自动修正maxTokens: ${config.maxTokens} -> ${correctedConfig.maxTokens}`
        );
      } else {
        correctedConfig.maxTokens = defaultAIConfig.maxTokens;
        console.log(`maxTokens无效，使用默认值: ${defaultAIConfig.maxTokens}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      correctedConfig: errors.length > 0 ? correctedConfig : undefined,
    };
  }

  // 获取预设配置
  static getPresetConfigs(): Array<{
    name: string;
    config: Partial<AIConfig>;
  }> {
    return [
      {
        name: "DeepSeek Chat（推荐）",
        config: {
          aiModel: "deepseek-chat",
          apiUrl: "https://api.deepseek.com/v1",
          temperature: 0.7,
          maxTokens: 1000,
        },
      },
      {
        name: "DeepSeek Coder",
        config: {
          aiModel: "deepseek-coder",
          apiUrl: "https://api.deepseek.com/v1",
          temperature: 0.3,
          maxTokens: 1500,
        },
      },
      {
        name: "OpenAI GPT-3.5 Turbo",
        config: {
          aiModel: "gpt-3.5-turbo",
          apiUrl: "https://api.openai.com/v1",
          temperature: 0.7,
          maxTokens: 1000,
        },
      },
      {
        name: "OpenAI GPT-4",
        config: {
          aiModel: "gpt-4",
          apiUrl: "https://api.openai.com/v1",
          temperature: 0.5,
          maxTokens: 1500,
        },
      },
      {
        name: "OpenAI GPT-4 Turbo",
        config: {
          aiModel: "gpt-4-turbo",
          apiUrl: "https://api.openai.com/v1",
          temperature: 0.5,
          maxTokens: 2000,
        },
      },
      {
        name: "OpenAI GPT-4o",
        config: {
          aiModel: "gpt-4o",
          apiUrl: "https://api.openai.com/v1",
          temperature: 0.7,
          maxTokens: 2000,
        },
      },
      {
        name: "Claude 3 Haiku",
        config: {
          aiModel: "claude-3-haiku",
          apiUrl: "https://api.anthropic.com/v1",
          temperature: 0.7,
          maxTokens: 1000,
        },
      },
      {
        name: "Claude 3 Sonnet",
        config: {
          aiModel: "claude-3-sonnet",
          apiUrl: "https://api.anthropic.com/v1",
          temperature: 0.5,
          maxTokens: 1500,
        },
      },
      {
        name: "Claude 3 Opus",
        config: {
          aiModel: "claude-3-opus",
          apiUrl: "https://api.anthropic.com/v1",
          temperature: 0.3,
          maxTokens: 2000,
        },
      },
      {
        name: "本地 Ollama（默认端口）",
        config: {
          aiModel: "llama-2-7b",
          apiUrl: "http://localhost:11434/v1",
          temperature: 0.7,
          maxTokens: 1000,
        },
      },
    ];
  }
}
