# CORS 问题解决方案

## 问题描述

在使用阿里云的 Dashscope API 时，遇到了 CORS（跨域资源共享）限制，具体错误如下：

```
Access to fetch at 'https://dashscope.aliyuncs.com/compatible-mode/v1//chat/completions' from origin 'http://localhost:5173' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

### 1. 配置 Vite 代理

在`vite.config.ts`中添加代理配置，将所有对 Dashscope API 的请求通过本地开发服务器代理：

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    strictPort: true,
    proxy: {
      "/api/dashscope": {
        target: "https://dashscope.aliyuncs.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/dashscope/, ""),
        secure: false,
      },
    },
  },
});
```

### 2. 修改 API 请求代码

在`AIService`类中添加一个代理 URL 处理方法：

```typescript
// 为API URL添加代理
private getProxyUrl(originalUrl: string): string {
  // 如果URL是DashScope的URL，使用我们的代理
  if (originalUrl.includes('dashscope.aliyuncs.com')) {
    return '/api/dashscope';
  }
  // 对于其他URL，暂时不做修改
  return originalUrl;
}
```

### 3. 应用到所有 API 请求

将所有对`this.config.apiUrl`的直接请求替换为使用代理 URL：

```typescript
// 使用代理地址替代直接调用
const apiUrlWithProxy = this.getProxyUrl(this.config.apiUrl);

const response = await fetch(`${apiUrlWithProxy}/chat/completions`, {
  // 其他请求配置保持不变
  ...
});
```

## 生产环境解决方案

对于生产环境，建议采用以下方案之一：

1. **后端代理**：建立一个后端服务，代理所有对 AI API 的请求
2. **云函数**：使用 AWS Lambda、Vercel Functions 或类似的无服务器函数来处理 API 请求
3. **API 网关**：配置专门的 API 网关处理跨域请求

## 其他可选解决方案

1. **CORS 浏览器插件**：在开发环境中使用允许 CORS 的浏览器插件（不推荐用于生产）
2. **本地 Node 代理服务器**：使用 Express 等创建一个本地代理服务器

## 注意事项

- 代理解决方案只适用于开发环境
- 不要在客户端代码中暴露敏感的 API 密钥
- 针对生产环境，应考虑更安全的认证方案
