# AI 便签功能实现指南

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- 现代浏览器支持 (Chrome 90+, Firefox 88+, Safari 14+)

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🔧 AI 功能配置

### 1. 支持的 AI 模型

#### DeepSeek (推荐)
```typescript
const deepSeekConfig: AIConfig = {
  enableAI: true,
  aiModel: "deepseek-chat",
  apiKey: "sk-your-deepseek-api-key",
  apiUrl: "https://api.deepseek.com/v1",
  temperature: 0.7,
  maxTokens: 1000
};
```

#### OpenAI GPT
```typescript
const openAIConfig: AIConfig = {
  enableAI: true,
  aiModel: "gpt-3.5-turbo",
  apiKey: "sk-your-openai-api-key", 
  apiUrl: "https://api.openai.com/v1",
  temperature: 0.7,
  maxTokens: 1000
};
```

#### Claude (Anthropic)
```typescript
const claudeConfig: AIConfig = {
  enableAI: true,
  aiModel: "claude-3-haiku",
  apiKey: "sk-your-anthropic-api-key",
  apiUrl: "https://api.anthropic.com/v1",
  temperature: 0.7,
  maxTokens: 1000
};
```

### 2. 配置步骤

1. **打开设置面板**: 点击右上角设置图标或按 `Ctrl/Cmd + ,`
2. **切换到 AI 设置**: 选择 "AI设置" 标签页
3. **启用 AI 功能**: 打开 "启用AI功能" 开关
4. **选择模型**: 从下拉菜单选择要使用的 AI 模型
5. **输入 API 密钥**: 填入对应服务商的 API 密钥
6. **设置 API 地址**: 确认 API 地址正确
7. **调整参数**: 根据需要调整温度和最大令牌数
8. **测试连接**: 点击 "测试连接" 验证配置
9. **保存配置**: 点击 "保存" 完成配置

## 📝 使用方法

### 基本操作

#### 1. 创建普通便签
- **方法一**: 双击画布空白区域
- **方法二**: 按快捷键 `N`
- **方法三**: 右键菜单选择 "新建便签"

#### 2. AI 生成便签
1. 在底部控制台输入描述文本
2. 点击 "AI生成" 按钮或按回车键
3. 等待 AI 处理并生成便签
4. 便签将自动出现在画布中心区域

#### 3. 编辑便签
- **编辑内容**: 双击便签内容区域
- **编辑标题**: 双击便签标题
- **调整大小**: 拖拽便签右下角
- **移动位置**: 拖拽便签标题栏

#### 4. 便签管理
- **删除便签**: 点击便签右上角 ❌ 按钮
- **改变颜色**: 点击便签颜色按钮选择新颜色
- **置顶便签**: 点击便签使其获得焦点

### 高级功能

#### 1. Markdown 支持
便签内容支持完整的 Markdown 语法：

```markdown
# 标题
## 二级标题

**粗体文本**
*斜体文本*

- 列表项 1
- 列表项 2

[链接](https://example.com)

`代码`

```code
代码块
```

> 引用文本
```

#### 2. 快捷键操作
- `N`: 新建便签
- `Ctrl/Cmd + S`: 保存所有便签
- `Ctrl/Cmd + Z`: 撤销操作
- `Ctrl/Cmd + Y`: 重做操作
- `Ctrl/Cmd + F`: 搜索便签
- `Ctrl/Cmd + ,`: 打开设置
- `Ctrl/Cmd + /`: 显示快捷键帮助
- `+`: 放大画布
- `-`: 缩小画布
- `0`: 重置缩放

#### 3. 画布操作
- **平移**: 按住空格键 + 鼠标拖拽
- **缩放**: 鼠标滚轮或触控板手势
- **框选**: 按住 Shift + 鼠标拖拽选择多个便签
- **批量操作**: 选中多个便签后可批量删除或移动

## 🎨 自定义配置

### 1. 便签样式配置

#### 颜色主题
```typescript
const colorThemes = {
  yellow: "#fef3c7",    // 黄色 - 一般记录
  blue: "#dbeafe",      // 蓝色 - 重要事项  
  green: "#d1fae5",     // 绿色 - 完成任务
  pink: "#fce7f3",      // 粉色 - 个人事务
  purple: "#e9d5ff"     // 紫色 - 创意想法
};
```

#### 字体设置
```typescript
const fontSettings = {
  fontSize: 14,           // 字体大小 (12-24px)
  fontFamily: "system-ui", // 字体系列
  lineHeight: 1.5         // 行高
};
```

### 2. 画布设置

#### 网格配置
```typescript
const gridSettings = {
  visible: true,          // 显示网格
  size: 20,              // 网格大小 (10-50px)
  color: "#e5e7eb",      // 网格颜色
  opacity: 0.5           // 网格透明度
};
```

#### 背景设置
```typescript
const canvasSettings = {
  backgroundColor: "#ffffff",  // 背景颜色
  backgroundImage: null,       // 背景图片
  backgroundPattern: "grid"    // 背景图案
};
```

### 3. AI 提示词自定义

#### 系统提示词模板
```typescript
const customSystemPrompt = `你是一个专业的便签助手。请根据用户输入生成高质量的便签内容。

输出要求：
1. 返回标准 JSON 数组格式
2. 每个便签包含：title（简洁标题）、content（详细内容）、color（颜色代码）、tags（标签数组）
3. 根据内容类型智能分类和配色
4. 内容要具体实用，标题要简洁明了
5. 合理添加相关标签，最多5个

示例输出：
[{
  "title": "项目会议",
  "content": "## 讨论要点\\n- 项目进度回顾\\n- 下周计划制定",
  "color": "#dbeafe",
  "tags": ["工作", "会议"]
}]`;
```

#### 内容分类规则
```typescript
const contentCategories = {
  work: {
    keywords: ["工作", "项目", "会议", "任务", "deadline"],
    color: "#dbeafe",
    tags: ["工作"]
  },
  study: {
    keywords: ["学习", "笔记", "课程", "考试", "复习"],
    color: "#d1fae5", 
    tags: ["学习"]
  },
  life: {
    keywords: ["生活", "购物", "家务", "健康", "运动"],
    color: "#fef3c7",
    tags: ["生活"]
  },
  idea: {
    keywords: ["想法", "创意", "灵感", "计划", "目标"],
    color: "#e9d5ff",
    tags: ["创意"]
  }
};
```

## 🔍 故障排除

### 常见问题

#### 1. AI 功能无法使用
**问题**: 点击 AI 生成按钮没有反应

**解决方案**:
1. 检查 AI 配置是否完整
2. 验证 API 密钥是否有效
3. 确认网络连接正常
4. 查看浏览器控制台错误信息

#### 2. 便签无法保存
**问题**: 编辑便签后内容丢失

**解决方案**:
1. 检查浏览器是否支持 IndexedDB
2. 清除浏览器缓存后重试
3. 确认存储空间充足
4. 检查是否在隐私模式下使用

#### 3. 画布操作卡顿
**问题**: 拖拽便签或缩放画布时卡顿

**解决方案**:
1. 减少画布上的便签数量
2. 关闭不必要的浏览器标签页
3. 更新浏览器到最新版本
4. 检查硬件加速是否开启

#### 4. AI 生成内容格式错误
**问题**: AI 返回的内容无法正确解析

**解决方案**:
1. 尝试更换不同的 AI 模型
2. 调整温度参数 (建议 0.3-0.7)
3. 简化输入描述的复杂度
4. 检查 API 服务状态

### 调试模式

#### 开启调试日志
```typescript
// 在浏览器控制台执行
localStorage.setItem('debug', 'true');
// 刷新页面后将显示详细日志
```

#### 查看存储数据
```typescript
// 查看 AI 配置
const config = await IndexedDBAISettingsStorage.loadConfig();
console.log('AI配置:', config);

// 查看便签数据  
const notes = await IndexedDBService.getAllNotes();
console.log('便签数据:', notes);
```

#### 重置应用状态
```typescript
// 清除所有数据（谨慎操作）
await IndexedDBService.clearAll();
await IndexedDBAISettingsStorage.clearConfig();
location.reload();
```

## 📚 API 参考

### AIService 类

#### 方法列表
```typescript
class AIService {
  // 构造函数
  constructor(config: AIConfig)
  
  // 更新配置
  updateConfig(config: AIConfig): void
  
  // 验证配置
  validateConfig(): boolean
  
  // 测试连接
  testConnection(): Promise<{ success: boolean; error?: string }>
  
  // 生成便签
  generateStickyNotes(prompt: string): Promise<{
    success: boolean;
    notes?: StickyNoteData[];
    error?: string;
  }>
  
  // 分析文本
  analyzeText(text: string): Promise<{
    success: boolean;
    suggestions?: {
      category: string;
      priority: "high" | "medium" | "low";
      color: string;
      tags: string[];
    };
    error?: string;
  }>
}
```

### 数据类型定义

#### StickyNote 接口
```typescript
interface StickyNote {
  id: string;                    // 唯一标识符
  x: number;                     // X 坐标
  y: number;                     // Y 坐标  
  width: number;                 // 宽度
  height: number;                // 高度
  content: string;               // 内容 (Markdown)
  title: string;                 // 标题
  isEditing: boolean;            // 是否正在编辑内容
  isTitleEditing: boolean;       // 是否正在编辑标题
  color: "yellow" | "blue" | "green" | "pink" | "purple"; // 颜色
  isNew: boolean;                // 是否为新建便签
  zIndex: number;                // 层级索引
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
}
```

#### AIConfig 接口
```typescript
interface AIConfig {
  enableAI: boolean;             // 是否启用 AI
  aiModel: string;               // AI 模型名称
  apiKey: string;                // API 密钥
  apiUrl: string;                // API 地址
  temperature: number;           // 温度参数 (0-1)
  maxTokens: number;             // 最大令牌数
}
```

## 🤝 贡献指南

### 开发环境设置
1. Fork 项目仓库
2. 克隆到本地: `git clone <your-fork-url>`
3. 安装依赖: `npm install`
4. 创建功能分支: `git checkout -b feature/your-feature`
5. 开发和测试
6. 提交更改: `git commit -m "feat: add your feature"`
7. 推送分支: `git push origin feature/your-feature`
8. 创建 Pull Request

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 配置的代码风格
- 编写清晰的注释和文档
- 为新功能添加相应的测试用例

### 提交信息规范
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

---

📧 如有问题或建议，请通过 GitHub Issues 联系我们。
